# EquipRent Web - 墨尔本器材租赁网站

这是一个基于 Next.js 14 构建的专业器材租赁网站，专为墨尔本地区的活动器材租赁服务而设计。

## 功能特点

- 🎯 **专业器材展示** - 投影仪、投影幕布、音响设备、摄影灯、相机配件
- 📱 **响应式设计** - 完美适配桌面端和移动端
- 🎨 **现代化UI** - 使用 Tailwind CSS 构建的美观界面
- 🔍 **器材详情** - 弹出式卡片展示详细信息
- ⭐ **评分系统** - 显示器材评分和用户评价
- 📍 **位置信息** - 显示器材所在位置
- 🏷️ **分类浏览** - 按器材类型分类展示

## 技术栈

- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **图标**: Lucide React
- **图片**: Next.js Image 组件
- **字体**: Inter (Google Fonts)

## 器材类型

1. **投影仪** - 商务和家用投影设备
2. **投影幕布** - 各种尺寸的便携式幕布
3. **音响设备** - 专业音响和便携音箱
4. **摄影灯** - LED摄影照明设备
5. **相机配件** - 稳定器、三脚架等

## 快速开始

### 安装依赖

```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 运行开发服务器

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

在浏览器中打开 [http://localhost:3000](http://localhost:3000) 查看结果。

### 构建生产版本

```bash
npm run build
npm run start
```

## 项目结构

```
├── app/                    # Next.js App Router
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # React 组件
│   ├── Header.tsx         # 导航栏
│   ├── Hero.tsx           # 主标题区域
│   ├── CategorySection.tsx # 分类展示
│   ├── EquipmentCard.tsx  # 器材卡片
│   ├── EquipmentModal.tsx # 器材详情弹窗
│   └── EquipmentGrid.tsx  # 器材网格布局
├── data/                  # 数据文件
│   └── equipment.ts       # 器材数据
├── types/                 # TypeScript 类型定义
│   └── equipment.ts       # 器材类型
├── public/                # 静态资源
└── ...配置文件
```

## 自定义配置

### 主题色

主题色已设置为 `#E94E22`，可在以下文件中修改：

- `tailwind.config.js` - Tailwind 配置
- `app/globals.css` - CSS 变量

### 器材数据

器材数据存储在 `data/equipment.ts` 文件中，包含：

- 器材基本信息
- 价格和租赁周期
- 图片和描述
- 技术规格
- 可用性状态

### 图片资源

当前使用 Unsplash 的示例图片，生产环境中应替换为实际的器材图片。

## 部署

### Vercel 部署

1. 将代码推送到 GitHub
2. 在 [Vercel](https://vercel.com) 中导入项目
3. 自动部署完成

### 其他平台

项目支持部署到任何支持 Next.js 的平台，如：

- Netlify
- AWS Amplify
- Railway
- 自托管服务器

## 开发说明

### 添加新器材

1. 在 `data/equipment.ts` 中添加器材数据
2. 确保图片路径正确
3. 更新分类计数

### 修改样式

- 使用 Tailwind CSS 类名
- 自定义样式添加到 `app/globals.css`
- 响应式设计已内置

### 添加新功能

- 组件放在 `components/` 目录
- 类型定义放在 `types/` 目录
- 数据文件放在 `data/` 目录

## 联系信息

- 电话: (03) 9000 0000
- 邮箱: <EMAIL>
- 服务时间: 周一至周日 8:00-20:00
- 服务区域: 墨尔本及周边地区

## 许可证

本项目仅供学习和演示使用。
