'use client';

import { equipmentCategories } from '@/data/equipment';

export default function CategorySection() {
  return (
    <section className="flex w-full justify-center py-[60px] bg-white">
      <div className="container-inner-full">
        <div className="text-center mb-12">
          <h2 className="text-heading text-orange-900 mb-4">
            器材分类
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            我们提供各种专业器材租赁服务，满足您不同活动的需求
          </p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
          {equipmentCategories.map((category) => (
            <a
              key={category.id}
              href={`#${category.id}`}
              className="group flex flex-col items-center p-6 bg-gray-50 rounded-2xl hover:bg-primary/5 hover:shadow-lg transition-all duration-300"
            >
              <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                {category.icon}
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2 text-center">
                {category.name}
              </h3>
              <p className="text-sm text-gray-600 text-center mb-3">
                {category.description}
              </p>
              <span className="text-xs text-primary font-medium">
                {category.count} 件器材
              </span>
            </a>
          ))}
        </div>
      </div>
    </section>
  );
}
