@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

:root {
  --primary-color: #E94E22;
  --primary-dark: #D63E12;
  --primary-light: #F26B42;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', sans-serif;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

.container-inner-full {
  @apply mx-auto px-4 sm:px-6 lg:px-8;
  max-width: 1320px;
}

.text-heading {
  @apply text-2xl md:text-3xl lg:text-4xl font-bold text-orange-900;
}

.text-heading-hero {
  @apply text-4xl md:text-5xl lg:text-6xl font-bold text-orange-900;
}

.text-heading-small {
  @apply text-lg md:text-xl font-semibold text-orange-900;
}

.text-body-small {
  @apply text-sm text-gray-600;
}

.text-callout {
  @apply text-orange-500;
}
