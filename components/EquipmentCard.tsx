'use client';

import Image from 'next/image';
import { Equipment } from '@/types/equipment';
import { Star, MapPin } from 'lucide-react';

interface EquipmentCardProps {
  equipment: Equipment;
  onClick: () => void;
}

export default function EquipmentCard({ equipment, onClick }: EquipmentCardProps) {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const getCategoryLabel = (category: string) => {
    const labels = {
      'projector': '投影仪',
      'screen': '投影幕布',
      'audio': '音响设备',
      'lighting': '摄影灯',
      'camera-accessories': '相机配件'
    };
    return labels[category as keyof typeof labels] || category;
  };

  return (
    <div 
      data-testid="equipment-card" 
      role="listitem" 
      className="relative flex h-full shrink-0 flex-col p-0 cursor-pointer hover:transform hover:scale-105 transition-transform duration-200"
      onClick={onClick}
    >
      <article role="article" className="relative" aria-label={`Equipment: ${equipment.name}`}>
        <div className="relative mb-5 w-full">
          <Image
            alt={equipment.name}
            width={400}
            height={300}
            className="w-full h-[300px] rounded-[15px] border-none object-cover align-middle"
            src={equipment.image}
          />
          {equipment.isNew && (
            <span 
              data-testid={`pillwithtext-flag-${equipment.id}`}
              className="box-border flex flex-row items-center justify-center rounded-3xl border-[1.5px] border-current px-[12px] py-[2px] font-[14px] leading-[20px] absolute bottom-[15px] left-[15px] border-primary bg-pink-light text-primary"
            >
              新品
            </span>
          )}
          {equipment.isFeatured && (
            <span 
              className="box-border flex flex-row items-center justify-center rounded-3xl border-[1.5px] border-current px-[12px] py-[2px] font-[14px] leading-[20px] absolute bottom-[15px] right-[15px] border-primary bg-primary text-white"
            >
              推荐
            </span>
          )}
        </div>
        <div className="flex flex-col gap-[10px]">
          <div className="flex gap-[15px] items-center">
            <h3 className="text-heading-small self-center truncate max-w-[150px] md:max-w-[200px] lg:max-w-[250px]">
              <span className="whitespace-nowrap break-words">
                ${equipment.price}
              </span>
              <span className="text-sm text-gray-500 ml-1">
                /{equipment.priceUnit === 'day' ? '天' : equipment.priceUnit === 'week' ? '周' : '月'}
              </span>
            </h3>
            <span className="box-border flex flex-row items-center justify-center rounded-3xl border-[1.5px] border-current px-[12px] py-[2px] font-[14px] leading-[20px] text-primary border-primary">
              {getCategoryLabel(equipment.category)}
            </span>
          </div>
          <h4 className="text-lg font-semibold text-gray-900 line-clamp-2">
            {equipment.name}
          </h4>
          <p data-testid="location-byline" className="text-body-small w-full flex items-center">
            <MapPin className="w-4 h-4 mr-1 text-gray-400" />
            {equipment.location}
          </p>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {renderStars(equipment.rating)}
              <span className="ml-2 text-sm text-gray-600">
                {equipment.rating} ({equipment.reviewCount})
              </span>
            </div>
            <span className={`px-2 py-1 rounded-full text-xs ${
              equipment.availability === 'available' 
                ? 'bg-green-100 text-green-800' 
                : equipment.availability === 'rented'
                ? 'bg-red-100 text-red-800'
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              {equipment.availability === 'available' ? '可租赁' : 
               equipment.availability === 'rented' ? '已租出' : '维护中'}
            </span>
          </div>
        </div>
      </article>
    </div>
  );
}
