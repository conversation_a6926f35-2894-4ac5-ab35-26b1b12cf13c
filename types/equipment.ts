export interface Equipment {
  id: string;
  name: string;
  category: 'projector' | 'screen' | 'audio' | 'lighting' | 'camera-accessories';
  subcategory?: string;
  price: number;
  priceUnit: 'day' | 'week' | 'month';
  image: string;
  images: string[];
  description: string;
  features: string[];
  specifications: Record<string, string>;
  availability: 'available' | 'rented' | 'maintenance';
  location: string;
  rating: number;
  reviewCount: number;
  isNew?: boolean;
  isFeatured?: boolean;
}

export interface EquipmentCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  count: number;
}

export interface RentalPeriod {
  start: Date;
  end: Date;
  equipment: Equipment;
  totalPrice: number;
}
