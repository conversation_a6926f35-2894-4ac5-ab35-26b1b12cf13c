'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Equipment } from '@/types/equipment';
import { X, Star, MapPin, Calendar, Phone } from 'lucide-react';

interface EquipmentModalProps {
  equipment: Equipment | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function EquipmentModal({ equipment, isOpen, onClose }: EquipmentModalProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  if (!isOpen || !equipment) return null;

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-5 h-5 ${
          i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const getCategoryLabel = (category: string) => {
    const labels = {
      'projector': '投影仪',
      'screen': '投影幕布',
      'audio': '音响设备',
      'lighting': '摄影灯',
      'camera-accessories': '相机配件'
    };
    return labels[category as keyof typeof labels] || category;
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
        />
        
        {/* Modal */}
        <div className="relative bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
          >
            <X className="w-6 h-6 text-gray-600" />
          </button>

          <div className="grid md:grid-cols-2 gap-8 p-6">
            {/* Images */}
            <div className="space-y-4">
              <div className="relative aspect-[4/3] rounded-lg overflow-hidden">
                <Image
                  src={equipment.images[selectedImageIndex] || equipment.image}
                  alt={equipment.name}
                  fill
                  className="object-cover"
                />
              </div>
              
              {equipment.images.length > 1 && (
                <div className="flex gap-2 overflow-x-auto">
                  {equipment.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedImageIndex(index)}
                      className={`relative w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 ${
                        selectedImageIndex === index ? 'ring-2 ring-primary' : ''
                      }`}
                    >
                      <Image
                        src={image}
                        alt={`${equipment.name} ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Details */}
            <div className="space-y-6">
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <span className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium">
                    {getCategoryLabel(equipment.category)}
                  </span>
                  {equipment.isNew && (
                    <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                      新品
                    </span>
                  )}
                  {equipment.isFeatured && (
                    <span className="px-3 py-1 bg-primary text-white rounded-full text-sm font-medium">
                      推荐
                    </span>
                  )}
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  {equipment.name}
                </h2>
                <div className="flex items-center gap-4 mb-4">
                  <div className="flex items-center">
                    {renderStars(equipment.rating)}
                    <span className="ml-2 text-gray-600">
                      {equipment.rating} ({equipment.reviewCount} 评价)
                    </span>
                  </div>
                </div>
                <div className="flex items-center text-gray-600 mb-4">
                  <MapPin className="w-5 h-5 mr-2" />
                  {equipment.location}
                </div>
              </div>

              <div>
                <div className="text-3xl font-bold text-primary mb-2">
                  ${equipment.price}
                  <span className="text-lg text-gray-500 font-normal">
                    /{equipment.priceUnit === 'day' ? '天' : equipment.priceUnit === 'week' ? '周' : '月'}
                  </span>
                </div>
                <span className={`inline-block px-3 py-1 rounded-full text-sm ${
                  equipment.availability === 'available' 
                    ? 'bg-green-100 text-green-800' 
                    : equipment.availability === 'rented'
                    ? 'bg-red-100 text-red-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {equipment.availability === 'available' ? '可租赁' : 
                   equipment.availability === 'rented' ? '已租出' : '维护中'}
                </span>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-3">产品描述</h3>
                <p className="text-gray-600 leading-relaxed">
                  {equipment.description}
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-3">主要特点</h3>
                <ul className="space-y-2">
                  {equipment.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-gray-600">
                      <span className="w-2 h-2 bg-primary rounded-full mr-3 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-3">技术规格</h3>
                <div className="space-y-2">
                  {Object.entries(equipment.specifications).map(([key, value]) => (
                    <div key={key} className="flex justify-between py-2 border-b border-gray-100">
                      <span className="text-gray-600">{key}</span>
                      <span className="font-medium">{value}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex gap-4 pt-4">
                <button className="flex-1 bg-primary text-white py-3 px-6 rounded-lg hover:bg-primary-dark transition-colors flex items-center justify-center gap-2">
                  <Calendar className="w-5 h-5" />
                  立即预订
                </button>
                <button className="px-6 py-3 border border-primary text-primary rounded-lg hover:bg-primary/5 transition-colors flex items-center gap-2">
                  <Phone className="w-5 h-5" />
                  咨询
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
