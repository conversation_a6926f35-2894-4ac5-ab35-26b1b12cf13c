'use client';

import { useState } from 'react';
import { Equipment } from '@/types/equipment';
import EquipmentCard from './EquipmentCard';
import EquipmentModal from './EquipmentModal';

interface EquipmentGridProps {
  equipment: Equipment[];
  title: string;
  categoryId?: string;
}

export default function EquipmentGrid({ equipment, title, categoryId }: EquipmentGridProps) {
  const [selectedEquipment, setSelectedEquipment] = useState<Equipment | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleEquipmentClick = (item: Equipment) => {
    setSelectedEquipment(item);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedEquipment(null);
  };

  return (
    <section 
      id={categoryId}
      data-testid="equipmentGrid" 
      role="article" 
      className="flex flex-col overflow-hidden bg-cream py-16"
    >
      <div className="container-inner-full gap-5 self-center">
        <article className="grid-default w-full self-center pb-[30px]">
          <h2 className="text-heading text-orange-900">
            {title}
          </h2>
        </article>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {equipment.map((item) => (
            <EquipmentCard
              key={item.id}
              equipment={item}
              onClick={() => handleEquipmentClick(item)}
            />
          ))}
        </div>
      </div>

      <EquipmentModal
        equipment={selectedEquipment}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </section>
  );
}
