/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: '#E94E22',
        'primary-dark': '#D63E12',
        'primary-light': '#F26B42',
        cream: '#F0E3D6',
        'orange-900': '#E94E22',
        'darkOrange-800': '#D63E12',
        'darkOrange-900': '#C73502',
        'pink-light': '#FFF5F2',
        'yellow-900': '#F59E0B',
        'blue-900': '#1E40AF',
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
    },
  },
  plugins: [],
}
