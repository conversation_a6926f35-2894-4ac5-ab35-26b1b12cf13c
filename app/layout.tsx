import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'EquipRent - 墨尔本专业器材租赁',
  description: '提供投影仪、音响、摄影灯、相机配件等专业器材租赁服务，为您的活动增色添彩。墨尔本本地服务，品质保证。',
  keywords: '器材租赁,投影仪租赁,音响租赁,摄影灯租赁,相机配件,墨尔本,活动器材',
  authors: [{ name: 'EquipRent Team' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'EquipRent - 墨尔本专业器材租赁',
    description: '提供投影仪、音响、摄影灯、相机配件等专业器材租赁服务，为您的活动增色添彩。',
    type: 'website',
    locale: 'zh_CN',
    siteName: 'EquipRent',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'EquipRent - 墨尔本专业器材租赁',
    description: '提供投影仪、音响、摄影灯、相机配件等专业器材租赁服务，为您的活动增色添彩。',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <head>
        <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
        <link rel="icon" type="image/png" href="/favicon.png" />
        <meta name="theme-color" content="#E94E22" />
      </head>
      <body className={inter.className}>
        <div className="min-h-screen bg-cream">
          {children}
        </div>
      </body>
    </html>
  );
}
