'use client';

import Image from 'next/image';
import Link from 'next/link';

export default function Header() {
  return (
    <header className="absolute z-40 mb-[8px] hidden w-full pt-5 sm:text-lg md:!visible md:!block xl:text-2xl">
      <section className="rent-home-menu">
        <div className="container-inner-full md:w-[740px] lg:w-[1320px] lg-8/9:w-[1180px]">
          <nav aria-label="Top">
            <div>
              <div className="flex h-16 grow items-center">
                <div data-testid="equipRentLogo" className="flex md:pr-[15.17px] lg:pr-[26px]">
                  <Link href="/" className="relative md:h-[35px] md:w-[200px] lg:h-[72px] lg:w-[300px] lg-8/9:h-[39.59px] lg-8/9:w-[250px]">
                    <div className="h-full w-full flex items-center">
                      <span className="text-2xl lg:text-4xl font-bold text-primary">
                        EquipRent
                      </span>
                      <span className="ml-2 text-sm lg:text-base text-gray-600">
                        墨尔本
                      </span>
                    </div>
                  </Link>
                </div>
                <div data-testid="navigationMenu" className="menu-text flex h-full grow items-center text-primary xs:text-base md:ml-[15px] md:mr-[39px] lg:ml-[72px] lg:mr-0 lg:w-[689px] lg-8/9:ml-[40px] lg-8/9:gap-[24px] md-only:gap-[5px]">
                  <nav className="hidden md:flex space-x-8">
                    <Link href="#projector" className="hover:text-primary-dark transition-colors">
                      投影仪
                    </Link>
                    <Link href="#screen" className="hover:text-primary-dark transition-colors">
                      投影幕布
                    </Link>
                    <Link href="#audio" className="hover:text-primary-dark transition-colors">
                      音响设备
                    </Link>
                    <Link href="#lighting" className="hover:text-primary-dark transition-colors">
                      摄影灯
                    </Link>
                    <Link href="#camera-accessories" className="hover:text-primary-dark transition-colors">
                      相机配件
                    </Link>
                  </nav>
                </div>
                <div data-testid="contactButton" className="flex">
                  <Link 
                    href="tel:+61-3-9000-0000" 
                    className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors"
                  >
                    联系我们
                  </Link>
                </div>
              </div>
            </div>
          </nav>
        </div>
      </section>
    </header>
  );
}
