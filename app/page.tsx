'use client';

import Header from '@/components/Header';
import Hero from '@/components/Hero';
import CategorySection from '@/components/CategorySection';
import EquipmentGrid from '@/components/EquipmentGrid';
import { equipmentList } from '@/data/equipment';

export default function Home() {
  // Group equipment by category
  const projectors = equipmentList.filter(item => item.category === 'projector');
  const screens = equipmentList.filter(item => item.category === 'screen');
  const audioEquipment = equipmentList.filter(item => item.category === 'audio');
  const lighting = equipmentList.filter(item => item.category === 'lighting');
  const cameraAccessories = equipmentList.filter(item => item.category === 'camera-accessories');

  // Featured equipment (first 6 items)
  const featuredEquipment = equipmentList.slice(0, 6);

  return (
    <div className="lg:min-h-screen">
      <div className="bg-cream">
        <Header />
        
        <Hero />
        
        {/* Service Info Section */}
        <section className="flex w-full justify-center py-[30px]">
          <div className="flex flex-col items-center">
            <div className="w-[245px] md:w-[471px]">
              <h2 className="w-full text-center text-lg font-medium not-italic leading-[22px] text-primary md:text-[24px] md:leading-[32px]">
                墨尔本专业器材租赁服务，为您的活动提供高品质设备支持
              </h2>
            </div>
            <div className="mt-5">
              <div className="grid">
                <p className="mb-1 text-center text-base font-medium leading-[22px] text-darkOrange-800">
                  客户满意度 4.9 分
                </p>
                <div className="flex justify-center">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      className="w-6 h-6 text-yellow-400 fill-current"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>

        <CategorySection />

        {/* Featured Equipment */}
        <EquipmentGrid
          equipment={featuredEquipment}
          title="精选器材推荐"
          categoryId="featured"
        />

        {/* Equipment by Category */}
        {projectors.length > 0 && (
          <EquipmentGrid
            equipment={projectors}
            title="投影仪设备"
            categoryId="projector"
          />
        )}

        {screens.length > 0 && (
          <EquipmentGrid
            equipment={screens}
            title="投影幕布"
            categoryId="screen"
          />
        )}

        {audioEquipment.length > 0 && (
          <EquipmentGrid
            equipment={audioEquipment}
            title="音响设备"
            categoryId="audio"
          />
        )}

        {lighting.length > 0 && (
          <EquipmentGrid
            equipment={lighting}
            title="摄影灯具"
            categoryId="lighting"
          />
        )}

        {cameraAccessories.length > 0 && (
          <EquipmentGrid
            equipment={cameraAccessories}
            title="相机配件"
            categoryId="camera-accessories"
          />
        )}

        {/* Contact Section */}
        <section className="bg-white py-16">
          <div className="container-inner-full text-center">
            <h2 className="text-heading text-orange-900 mb-6">
              联系我们
            </h2>
            <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              需要咨询器材租赁或有特殊需求？我们的专业团队随时为您服务
            </p>
            <div className="flex flex-col md:flex-row gap-4 justify-center items-center">
              <a
                href="tel:+61-3-9000-0000"
                className="bg-primary text-white px-8 py-3 rounded-lg hover:bg-primary-dark transition-colors text-lg font-medium"
              >
                📞 (03) 9000 0000
              </a>
              <a
                href="mailto:<EMAIL>"
                className="border border-primary text-primary px-8 py-3 rounded-lg hover:bg-primary/5 transition-colors text-lg font-medium"
              >
                📧 <EMAIL>
              </a>
            </div>
            <p className="text-gray-500 mt-6">
              服务时间：周一至周日 8:00-20:00 | 墨尔本本地配送
            </p>
          </div>
        </section>
      </div>
    </div>
  );
}
